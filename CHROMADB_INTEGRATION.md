# ChromaDB Integration for Windows

This document describes the ChromaDB integration that replaces the MockEmbeddingManager on Windows systems.

## Overview

Previously, the application used Milvus Lite for vector database operations on Ubuntu/macOS but fell back to a MockEmbeddingManager on Windows for testing purposes. This implementation replaces the mock with a fully functional ChromaDB-based embedding manager that provides the same functionality as the Mi<PERSON><PERSON>s implementation.

## Changes Made

### 1. Dependencies
- Added `chromadb==0.5.23` to `requirements_simple.txt`

### 2. New ChromaEmbeddingManager Class
Created a new `ChromaEmbeddingManager` class in `app/core/embeddings_management.py` that:
- Uses ChromaDB as the vector database backend
- Implements the same interface as the existing embedding manager
- Supports all operations: create_collection, upsert_data, load_collection, search, query, delete_collection
- Uses SentenceTransformer for embedding generation (same as the mock)
- Persists data to disk using ChromaDB's persistent client

### 3. Updated Platform Detection
Modified the `EmbeddingManager` class to:
- Use `ChromaEmbeddingManager` instead of `MockEmbeddingManager` on Windows
- Maintain backward compatibility with Mil<PERSON><PERSON> on Ubuntu/macOS
- Fall back to ChromaDB if Mi<PERSON><PERSON>s initialization fails

### 4. Key Features
- **Persistent Storage**: Data is stored in `./chroma_db` directory by default
- **Cosine Similarity**: Uses cosine similarity for vector search (same as Milvus)
- **Metadata Support**: Supports filtering and querying with metadata
- **Batch Processing**: Processes large datasets in batches of 5,000 records for efficiency
- **Progress Feedback**: Provides batch progress messages during large operations
- **Memory Efficient**: Prevents memory issues when processing very large datasets
- **Compatible Interface**: Drop-in replacement for MockEmbeddingManager

## Usage

The integration is transparent to existing code. On Windows systems, the application will automatically use ChromaDB:

```python
from app.core.embeddings_management import EmbeddingManager

# This will use ChromaDB on Windows, Milvus on Ubuntu/macOS
embedding_manager = EmbeddingManager()

# All existing operations work the same way
embedding_manager.create_collection("my_collection")
embedding_manager.upsert_data("my_collection", data)
results = embedding_manager.search("my_collection", ["query text"])
```

## Data Schema

The ChromaDB implementation supports the same data schema as Milvus:
- `label_id`: Primary key (integer)
- `name`: Text field for embedding generation (string)
- `category`: Category field for filtering (string)
- Additional metadata fields are supported

## Performance

ChromaDB provides:
- Fast similarity search using HNSW indexing
- Efficient storage and retrieval
- Good performance for datasets up to millions of vectors
- Automatic indexing and optimization
- Batch processing with 5,000 records per batch for optimal memory usage
- Progress tracking for long-running operations

## Testing

A comprehensive test suite verifies all functionality:

### Basic Functionality Tests (`test_chromadb_implementation.py`)
- Basic ChromaDB functionality
- EmbeddingManager integration
- Search and query operations
- Filter functionality
- Data persistence

### Batch Processing Tests (`test_batch_processing.py`)
- Small dataset processing (< 5,000 records)
- Large dataset processing (25,000 records in 5 batches)
- Boundary condition testing (10,000 records in 2 batches)
- Progress message verification
- Memory efficiency validation

Run tests with:
```bash
python test_chromadb_implementation.py
python test_batch_processing.py
```

## File Structure

```
app/core/embeddings_management.py  # Updated with ChromaEmbeddingManager
requirements_simple.txt            # Added chromadb dependency
test_chromadb_implementation.py    # Basic functionality test suite
test_batch_processing.py           # Batch processing test suite
chroma_db/                         # ChromaDB data directory (created automatically)
```

## Benefits

1. **Full Functionality**: Replaces mock with real vector database operations
2. **Cross-Platform**: Works on Windows, macOS, and Linux
3. **Easy Installation**: No complex dependencies or system requirements
4. **Persistent Storage**: Data survives application restarts
5. **Production Ready**: Suitable for production use on Windows
6. **Efficient Batch Processing**: Handles large datasets without memory issues
7. **Progress Tracking**: Provides feedback during long operations
8. **Optimized Performance**: Uses appropriate batch sizes for ChromaDB's limits

## Migration

No migration is needed. The change is automatic:
- Existing Milvus installations continue to work on Ubuntu/macOS
- Windows systems automatically use ChromaDB
- All existing code continues to work without changes

## Troubleshooting

### Common Issues

1. **Permission Errors on Windows**: ChromaDB may leave temporary files that can't be immediately deleted. This is normal and doesn't affect functionality.

2. **Model Download**: First run will download the SentenceTransformer model (~90MB). Ensure internet connectivity.

3. **Disk Space**: ChromaDB stores data persistently. Ensure adequate disk space for your datasets.

### Configuration

The ChromaDB directory can be customized by modifying the `persist_directory` parameter in the `ChromaEmbeddingManager` constructor.

## Future Enhancements

Potential improvements:
- Configurable similarity metrics
- Advanced filtering capabilities
- Performance optimizations for large datasets
- Integration with ChromaDB's authentication features
