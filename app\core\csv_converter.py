import csv
import json
import os
import re


def sanitizer(text):
    """Sanitize field names to contain only alphanumeric characters and underscores."""
    # Replace any non-alphanumeric characters (except underscores) with underscores
    return re.sub(r'[^a-zA-Z0-9_]', '_', text)

def csv_to_json(csv_file_path, delimiter='|', has_header=True):
    # Initialize an empty list to store the data
    data = []
    
    # Initialize a counter for the label_id field
    label_id_counter = 1
    
    # Open the CSV file
    with open(csv_file_path, mode='r', encoding='utf-8') as csv_file:
        # Create a CSV reader object
        csv_reader = csv.reader(csv_file, delimiter=delimiter)
        
        # Read the first row to check if it's a header
        first_row = next(csv_reader, None)
        
        # Determine field names based on the has_header parameter
        if has_header and first_row:
            # Sanitize field names and replace the first field name with 'name'
            field_names = ['name'] + [sanitizer(field) for field in first_row[1:]]
        else:
            # Use default field names (name, Field2, Field3, etc.)
            field_names = ['name'] + [f'Field{i}' for i in range(2, len(first_row) + 1)] if first_row else ['name']
            # If the first row is not a header, add it as a data item
            if first_row:
                record = {'label_id': label_id_counter}  # Add label_id
                label_id_counter += 1  # Increment the counter
                for i, value in enumerate(first_row):
                    if value == '':  # Handle empty fields
                        record[field_names[i]] = None
                    else:
                        record[field_names[i]] = value
                data.append(record)
        
        # Iterate over the remaining rows in the CSV
        for row in csv_reader:
            # Skip empty rows
            if not row:
                continue
            
            # Create a record using the field names
            record = {'label_id': label_id_counter}  # Add label_id
            label_id_counter += 1  # Increment the counter
            for i, value in enumerate(row):
                if value == '':  # Handle empty fields
                    record[field_names[i]] = None
                else:
                    record[field_names[i]] = value
            
            # Append the record to the data list
            data.append(record)
    
    # Return the JSON data
    return data

if __name__ == '__main__':
    # Get the current directory where the script is located
    current_directory = os.path.dirname(os.path.abspath(__file__))
    
    # Define the file path for the CSV file
    label_pool_path = os.path.join(current_directory, 'label_pool_sample.csv')
    
    # Ask the user if the CSV file has a header
    has_header_input = input("Does the CSV file have a header? (yes/no): ").strip().lower()
    has_header = has_header_input == 'yes'
    
    # Call the function and print the JSON data
    json_data = csv_to_json(label_pool_path, delimiter='#', has_header=has_header)
    print(json_data)