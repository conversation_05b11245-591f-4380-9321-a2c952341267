from pydantic import BaseModel, create_model
from typing import List

def create_keywords_extraction_class(category_names: list[str]) -> type:
    """
    Dynamically creates a Pydantic BaseModel subclass with fields defined by category_names.
    
    Args:
        category_names (list[str]): A list of strings representing the category names for the fields.
        
    Returns:
        type: A new Pydantic BaseModel subclass.
    """
    # Define the default field 'keywords'
    fields = {'keywords': (List[str], ...)}

    # Add additional fields based on the provided category names
    for name in category_names:
        sanitized_name = ''.join([c if c.isalnum() or c == '_' else '_' for c in name])
        fields[sanitized_name] = (List[str], ...)

    # Create and return the new model
    return create_model('KeywordsExtraction', **fields)

if __name__ == '__main__':
    category_names = ['persons_organizations', 'times_places']
    KeywordsExtraction = create_keywords_extraction_class(category_names)

    # Now you can create instances of KeywordsExtraction
    instance = KeywordsExtraction(keywords=['keyword1', 'keyword2'],
                                persons_organizations=['person1', 'org1'],
                                times_places=['time1', 'place1'])

    print(instance)