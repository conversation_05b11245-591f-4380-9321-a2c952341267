from openai import OpenAI
from pydantic import BaseModel
import os
from dotenv import load_dotenv

# This class is for getting structured data from OpenAI's API. The data_structure_class should be a Pydantic model.

load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
# os.environ['OPENAI_API_KEY'] = "********************************************************"

class GPTResponse:

    def get_response(self, prompt_text, data_structure_class=None, model="gpt-4o-mini", debug=False):
        try:
            client = OpenAI()
            completion = client.beta.chat.completions.parse(
                model=model,
                messages=[
                    {"role": "system", "content": "You are a historian and an expert at producing structured data."},
                    {"role": "user", "content": prompt_text}
                ],
                response_format=data_structure_class,
            )
            if debug:
                print('Model used:', model)
                print(completion)
                

            message = completion.choices[0].message
            if (message.refusal):
                print("Model refused to respond.")
                return "refused"
            else:
                return message.parsed

        except Exception as e:
            print(e)
            return None



if __name__ == "__main__":
    title = "Mobilizing Citizens' Ears: Aural Training as Civil Defense, 1941–45"
    abstract = '''In the mid-twentieth century, the Japanese state trained its citizens to identify the sound of a falling bomb. This article explores the little-studied impact of audio-based military technology on wider society, beyond the military sector and beyond Western contexts. In 1941, Japan's elementary schools—renamed National People's Schools—provided new musical training in perfect pitch to strengthen Japan's national defense efforts in wartime. "Explosive sound training" taught children to identify recorded explosive sounds of enemy aircraft, though ultimately such training could not mitigate widespread destruction. This article argues that the recent discussion of "the story of acoustic defense" can benefit from a much broader historiographical framework, one focused not just on the military frame. In the Japanese case, the story was shaped through the active interactions between the military, society, and educational institutions. A multidisciplinary analytical perspective provides a more critical and nuanced understanding of how modern sound-politics overlapped with exercising wartime power.'''
    top_n=20
    if abstract is None:
        prompt_text = f"""Given the following academic article, identify the top {top_n} most relevant keywords, extract names of people and organizations, and extract the relevant time periods and places.\n\nArticle title: {title}."""
    else:
        prompt_text = f"""Given the following academic article, generate the top {top_n} most relevant keywords, extract names of people and organizations, and return the relevant time periods and places.\n\nArticle title: {title}\n\nArticle abstract: {abstract}."""

    class KeywordsExtraction(BaseModel):
        keywords: list[str]
        names_entities: list[str]
        times_and_places: list[str]

    gpt_generate_keywords = GPTResponse()

    structured_text = gpt_generate_keywords.get_response(prompt_text, data_structure_class=KeywordsExtraction, model="gpt-4o-mini", debug=True)
    print("Keywords:", structured_text.keywords)
    print("People and organizations:", structured_text.names_entities)
    print("Times and places:", structured_text.times_and_places)
