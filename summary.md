This application is a Flask-based API that provides automatic tagging for academic articles. It uses natural language processing (NLP) and machine learning models to generate relevant keywords and entities from the text.

The system offers two main pipelines for generating tags:

1.  **TextRank + Sentence Transformer:** This pipeline operates entirely locally. It uses the TextRank algorithm to extract keywords and spaCy for named entity recognition (persons, organizations, dates, and places). Then, it uses a Sentence Transformer model to find the most similar labels from a predefined "label pool" by comparing vector embeddings.

2.  **LLM + Sentence Transformer:** This pipeline leverages a large language model (LLM) like GPT-4o for more advanced keyword extraction and filtering. It follows a three-step process:
    *   The LLM generates an initial set of keywords and entities from the article's title and abstract.
    *   A Sentence Transformer matches these generated terms with labels from the label pool based on semantic similarity.
    *   The LLM refines the matched labels, removing irrelevant ones and sorting them by relevance.

The core of the application is the `EmbeddingManager`, which handles a Milvus vector database for efficient similarity searches. It can create collections, insert data with their vector embeddings, and perform searches to find the most relevant labels for a given text.

The API exposes the following endpoints:

*   `/generate_tags`: The main endpoint for generating tags. It accepts a JSON payload with article data and returns a list of tags categorized as "matched_tags", "concept_tags", "person_org_tags", and "time_place_tags".
*   `/generate_tags_test`: A test endpoint that returns a set of mock tags for development and testing purposes.
*   `/tags/all`: Retrieves all the labels currently available in the label pool.
*   `/health`: A simple health check endpoint to confirm that the API is running.

The application is configured to load a default label pool from a CSV file, which is then converted to a JSON format and stored in the Milvus vector database. The configuration also specifies the models to be used for sentence transformation and NLP processing.
