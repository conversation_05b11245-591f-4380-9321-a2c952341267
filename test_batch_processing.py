#!/usr/bin/env python3
"""
Test script for ChromaDB batch processing functionality.
This script tests that the ChromaEmbeddingManager correctly processes large datasets in batches.
"""

import os
import sys
import tempfile
import shutil
from typing import List, Dict

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.embeddings_management import ChromaEmbeddingManager

def generate_test_data(num_records: int) -> List[Dict]:
    """Generate test data with the specified number of records."""
    test_data = []
    categories = ["Concept", "Person", "Place", "Technology", "Science"]
    
    for i in range(num_records):
        category = categories[i % len(categories)]
        test_data.append({
            "label_id": i + 1,
            "name": f"Test Item {i + 1}",
            "category": category,
            "description": f"This is test item number {i + 1} in category {category}"
        })
    
    return test_data

def test_batch_processing_small_dataset():
    """Test batch processing with a small dataset (should be 1 batch)."""
    print("=" * 60)
    print("Testing Batch Processing - Small Dataset (< 10,000 records)")
    print("=" * 60)
    
    temp_dir = tempfile.mkdtemp(prefix="chromadb_batch_test_small_")
    print(f"Using temporary directory: {temp_dir}")
    
    try:
        # Initialize ChromaDB manager
        chroma_manager = ChromaEmbeddingManager(
            embedding_model="all-MiniLM-L6-v2",
            persist_directory=temp_dir
        )
        
        # Generate small test dataset (500 records)
        test_data = generate_test_data(500)
        collection_name = "small_batch_test"
        
        print(f"\n1. Testing with {len(test_data)} records (should be 1 batch)...")
        
        # Create collection
        result = chroma_manager.create_collection(collection_name)
        assert result == "success", f"Expected 'success', got '{result}'"
        
        # Test batch processing
        print("\n2. Processing data in batches...")
        chroma_manager.upsert_data(collection_name, test_data)
        
        # Verify data was inserted
        print("\n3. Verifying data insertion...")
        query_results = chroma_manager.query(collection_name, None, ["name"])
        assert len(query_results) == len(test_data), f"Expected {len(test_data)} records, got {len(query_results)}"
        
        print(f"✅ Successfully processed {len(test_data)} records in 1 batch")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except PermissionError:
            print(f"Warning: Could not clean up {temp_dir} due to Windows file locks")
    
    return True

def test_batch_processing_large_dataset():
    """Test batch processing with a large dataset (should be multiple batches)."""
    print("\n" + "=" * 60)
    print("Testing Batch Processing - Large Dataset (> 10,000 records)")
    print("=" * 60)
    
    temp_dir = tempfile.mkdtemp(prefix="chromadb_batch_test_large_")
    print(f"Using temporary directory: {temp_dir}")
    
    try:
        # Initialize ChromaDB manager
        chroma_manager = ChromaEmbeddingManager(
            embedding_model="all-MiniLM-L6-v2",
            persist_directory=temp_dir
        )
        
        # Generate large test dataset (25,000 records = 5 batches with 5000 batch size)
        test_data = generate_test_data(25000)
        collection_name = "large_batch_test"

        print(f"\n1. Testing with {len(test_data)} records (should be 5 batches)...")
        
        # Create collection
        result = chroma_manager.create_collection(collection_name)
        assert result == "success", f"Expected 'success', got '{result}'"
        
        # Test batch processing
        print("\n2. Processing data in batches...")
        print("   (Watch for batch progress messages)")
        chroma_manager.upsert_data(collection_name, test_data)
        
        # Verify data was inserted
        print("\n3. Verifying data insertion...")
        query_results = chroma_manager.query(collection_name, None, ["name"])
        assert len(query_results) == len(test_data), f"Expected {len(test_data)} records, got {len(query_results)}"
        
        print(f"✅ Successfully processed {len(test_data)} records in 5 batches")
        
        # Test search functionality with large dataset
        print("\n4. Testing search with large dataset...")
        search_results = chroma_manager.search(collection_name, ["Test Item"], top_k=5)
        assert len(search_results) > 0, "Should return search results"
        print(f"✅ Search returned {len(search_results)} results")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except PermissionError:
            print(f"Warning: Could not clean up {temp_dir} due to Windows file locks")
    
    return True

def test_batch_processing_exact_boundary():
    """Test batch processing with exactly 10,000 records (boundary condition)."""
    print("\n" + "=" * 60)
    print("Testing Batch Processing - Large Boundary (10,000 records)")
    print("=" * 60)
    
    temp_dir = tempfile.mkdtemp(prefix="chromadb_batch_test_boundary_")
    print(f"Using temporary directory: {temp_dir}")
    
    try:
        # Initialize ChromaDB manager
        chroma_manager = ChromaEmbeddingManager(
            embedding_model="all-MiniLM-L6-v2",
            persist_directory=temp_dir
        )
        
        # Generate exactly 10,000 records (should be 2 batches with 5000 batch size)
        test_data = generate_test_data(10000)
        collection_name = "boundary_batch_test"

        print(f"\n1. Testing with {len(test_data)} records (should be 2 batches)...")
        
        # Create collection
        result = chroma_manager.create_collection(collection_name)
        assert result == "success", f"Expected 'success', got '{result}'"
        
        # Test batch processing
        print("\n2. Processing data in batches...")
        chroma_manager.upsert_data(collection_name, test_data)
        
        # Verify data was inserted
        print("\n3. Verifying data insertion...")
        query_results = chroma_manager.query(collection_name, None, ["name"])
        assert len(query_results) == len(test_data), f"Expected {len(test_data)} records, got {len(query_results)}"
        
        print(f"✅ Successfully processed {len(test_data)} records in 2 batches")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except PermissionError:
            print(f"Warning: Could not clean up {temp_dir} due to Windows file locks")
    
    return True

def main():
    """Run all batch processing tests."""
    print("ChromaDB Batch Processing Test Suite")
    print("=" * 80)
    
    all_tests_passed = True
    
    # Test 1: Small dataset (< 10,000 records)
    if not test_batch_processing_small_dataset():
        all_tests_passed = False
    
    # Test 2: Large dataset (> 10,000 records)
    if not test_batch_processing_large_dataset():
        all_tests_passed = False
    
    # Test 3: Exact boundary (10,000 records)
    if not test_batch_processing_exact_boundary():
        all_tests_passed = False
    
    # Final result
    print("\n" + "=" * 80)
    if all_tests_passed:
        print("🎉 ALL BATCH PROCESSING TESTS PASSED!")
        print("✅ ChromaDB batch processing is working correctly.")
    else:
        print("❌ SOME BATCH PROCESSING TESTS FAILED!")
    print("=" * 80)
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
